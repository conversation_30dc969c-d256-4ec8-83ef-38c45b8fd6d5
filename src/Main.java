//Main.java
//this is the driver class

public class Main {

    public static void main(String[] args) {
        //build MaxHeap object
        MaxHeap heap = new MaxHeap();
        System.out.println("print heap for each iteration");
        //add a few element
        heap.add(80);
        System.out.println("\nadd 80");
        heap.printHeap();

        heap.add(75);
        System.out.println("\nadd 75");
        heap.printHeap();

        heap.add(60);
        System.out.println("\nadd 60");
        heap.printHeap();

        heap.add(68);
        System.out.println("\nadd 68");
        heap.printHeap();

        heap.add(55);
        System.out.println("\nadd 55");
        heap.printHeap();

        heap.add(40);
        System.out.println("\nadd 40");
        heap.printHeap();

        heap.add(52);
        System.out.println("\nadd 52");
        heap.printHeap();

        heap.add(67);
        System.out.println("\nadd 67");
        heap.printHeap();

        System.out.println();
        System.out.println("this is final heap");

        //print the heap
        heap.printHeap();

        //observe the largest value in the heap
        System.out.print("\nlargest value: ");
        System.out.println(heap.peek());

        //remove the max value and print heap
        heap.remove();
        System.out.println("\nheap after the removal");
        heap.printHeap();

        //check what is current max
        System.out.print("\nlargest value: ");
        System.out.println(heap.peek());

    }
}
