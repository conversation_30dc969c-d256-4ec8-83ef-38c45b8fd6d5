//in this project, we will implement MaxHeap
//Please create the project called MaxHeap. MaxHeap has two files: 1) MaxHeap.java and 2) Main.java

public class MaxHeap {

	//heapArray is used to store heap using array implementation
	private int[] heapArray;
	//we will store maximum 100 elements in the array
	private final int DEFAULT_CAP = 100;
	//size is the next available position
	private int size = 0;

	//constructor
	public MaxHeap() {
		//create the array to store elements in the heap
		heapArray = new int[DEFAULT_CAP];
	}

	//add the value at the end of heap
	//if it is not the only item, check to make sure
	//the heap is still a maxheap. we will call
	//upheap method
	public void add(int value) {
		//if there is room in the array to place the next item
		if (size < heapArray.length) {
			//put the item in the next available spot
			heapArray[size] = value;

			//if it is not the only item, check to make sure
			//the heap is still a maxheap. we will call
			//upheap method
			if (size > 0)
				upheap();

			size++;
		}
	}

	//The process of adding an element and preserving the heap property.
	//
	//1. Add element to last location in heap
	//2. Compare element with its parent
	//   If it’s in the correct order, stop
	//   If not, swap
	//3. Repeat as needed
	//upheap is used to preserve the heap property when you add new item.
	private void upheap() {
		int index = size;
		int parentIndex = (index - 1) / 2;

		//if index is greater than 0 and value of child is greater than the parent
		//swap the child and parent
		while (index > 0 && heapArray[index] > heapArray[parentIndex]) {
			swap(index, parentIndex);

			//move up one level in the tree
			index = parentIndex;
			parentIndex = (index - 1) / 2;

		}
	} //end of upheap method

	//remove method will remove first element in the array and make sure
	//that heap property will be preserved
	//call downheap to store heap order
	public int remove() {
		//result will save the first element in the heap, which is
		//max value.
		int result = heapArray[0];

		//move the last element to the first element
		heapArray[0] = heapArray[size - 1];
		size--;

		//call downheap to store heap order
		downheap();

		return result;
	}

	//The process of removing element and preserving the heap property.
	//1. Replace the root with the last element (so it can be removed)
	//2. Compare new root with its (max) child
	//3. If it’s in the correct order, stop
	//   If not, swap
	//Repeat as needed

	//downheap will preserve the heap property when you remove the element at the top of heap
	private void downheap() {
		//index represents index for the parent
		int index = 0;
		//LC is the index for left child
		int LC = 2 * index + 1;
		//RC is the index of right child
		int RC = 2 * index + 2;

		//LC is within the boundary of heap
		//either left child or right child is greater than parent
		while (LC < size && (heapArray[LC] > heapArray[index] ||
							 heapArray[RC] > heapArray[index])) {

			// largest represents index of where the largest value is
			//assume it's the left child to start, then check if RC is larger
			int largest = LC;

			//if there is a RC and RC is larger
			if (RC < size && heapArray[RC] > heapArray[LC])
				largest = RC;

			//check if the largest child is greater than the parent,
			//swap the larger child with parent
			if (heapArray[largest] > heapArray[index])
				swap(largest, index);

			//update references to repeat process
			//largest represents current parent.
			index = largest;
			LC = 2 * index + 1;
			RC = 2 * index + 2;
		}
	}
	
	public boolean isEmpty() {
		return size == 0;

	}

	//swap two elements in the array
	private void swap(int index1, int index2) {
		int tmp = heapArray[index1];
		heapArray[index1] = heapArray[index2];
		heapArray[index2] = tmp;
	}

	//print the content of heap
	public void printHeap() {
		for (int i = 0; i < size; i++) {
			System.out.print(heapArray[i]);
			System.out.print(", ");
		}
		System.out.println();
	}

	//peek will return the first element in the array
	public int peek() {
		if (isEmpty()) {
			throw new IndexOutOfBoundsException("Heap is empty");
		}
		return heapArray[0];
	}

} //end of MaxHeap class
